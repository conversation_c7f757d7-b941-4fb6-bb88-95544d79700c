import * as path from 'path';
import * as crypto from "crypto";
import { Types } from "mongoose";
import { connectToMongo, closeConnection } from '../../common/database/db.module';
import { parseCSVWithRowNumbers } from '../../common/utils/csv-parser';
import { User, IUser as UserDocument } from './user.model';
import { Role, IRole as RoleDocument } from '../role/role.model';
import { LoggerConfig } from '../../common/logger/log.module';
import { Staff } from './staff.model';
import { Client } from './client.model';
import { ENUM_ROLE_TYPE } from '../role/role.enum';
import { ENUM_GENDER, ENUM_RELATION } from '../../common/enums/enums';
import uuid4 from "uuid4";
import { IFacility } from '../facility/facility.model';

interface ICsvUser {
  id: string;
  firstName: string;
  lastName: string;
  countryCode?: string;
  mobile: string;
  email: string;
  relation?: ENUM_RELATION;
  dob?: string;
  gender?: ENUM_GENDER;
  role: string;
  facilityId?: string;
  age?: number;
  isActive?: boolean;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  photo?: string;
  emergencyContactPerson?: string;
  emergencyContactPhone?: string;
  createdAt?: string;
}

const CsvToObjectKeyMapUser: Record<keyof ICsvUser, string> = {
  id: "id",
  firstName: "first name",
  lastName: "last name",
  countryCode: "country code",
  mobile: "mobile",
  email: "email",
  relation: "relation",
  dob: "dob",
  gender: "gender",
  role: "role",
  facilityId: "facility id",
  age: "age",
  isActive: "is active",
  address1: "address 1",
  address2: "address 2",
  city: "city",
  state: "state",
  postalCode: "postal code",
  country: "country",
  photo: "photo",
  emergencyContactPerson: "emergency contact person",
  emergencyContactPhone: "emergency contact phone",
  createdAt: "created at",
}

const logger = LoggerConfig('user.migration');
const duplicateClientsIds = new Set<string>();

/**
 * Migrate user data from CSV to MongoDB using Mongoose
 */
export async function migrateUsers(_dbName: string = 'hop-migration', session?: any): Promise<void> {
  try {
    logger.log('Starting user migration...');

    // Connect to database first
    await connectToMongo();

    try {
      // Then validate and get users
      const users = await getUsers();

      if (!users || users.length === 0) {
        logger.log('No valid users to migrate');
        return;
      }
    } catch (error) {
      logger.error('Error in migration process:', error);
      throw error;
    } finally {
    }
  } catch (error) {
    logger.error('Error migrating users:', error);
  } finally {
    // Only close the connection at the very end
    await closeConnection();
  }
}

const getUsers = async (): Promise<UserDocument[]> => {
  const mongoose = await connectToMongo();
  const session = await mongoose.startSession();

  try {
    // Start a transaction
    session.startTransaction();

    // Parse CSV file with row numbers
    const userDataWithRows = await parseCSVWithRowNumbers<ICsvUser>('users.csv', CsvToObjectKeyMapUser);

    if (!userDataWithRows || userDataWithRows.length === 0) {
      logger.info('No user data found in CSV file');
      return [];
    }

    logger.info(`Found ${userDataWithRows.length} users in CSV file`);

    const role = await Role.find().exec();
    const roleObj: { [ENUM_ROLE_TYPE: string]: Types.ObjectId } = role.reduce((pv, role: RoleDocument,) => ({ ...pv, [role.type]: role._id }), {});

    // Validate data and track parent-child relationships
    const errors = [];
    const usernames = [];
    const users = [];
    const staffs = [];
    const clients = [];

    // Track email/mobile to user mapping for parent-child relationships
    const emailMobileMap = new Map<string, { userDoc: UserDocument, rowNumber: number }>();
    const parentChildMap = new Map<string, Types.ObjectId>(); // child identifier -> parent ObjectId

    for (const { data: user, rowNumber } of userDataWithRows) {
      if (!user.firstName) {
        errors.push(`Row ${rowNumber}: User with id ${user.id} has no name`);
      }
      if (!user.email && !user.mobile) {
        errors.push(`Row ${rowNumber}: User with id ${user.id} has no email or mobile`);
      }

      if (user.email || user.mobile) {
        usernames.push(user.email || user.mobile);
      }

      const userDoc = new User({
        organizationId: new Types.ObjectId(global.config.organizationId),
        id: user.id,
        name: `${user.firstName} ${user.lastName}`,
        firstName: user.firstName,
        lastName: user.lastName,
        countryCode: user.countryCode,
        mobile: user.mobile,
        email: user.email, //? user.email.split("@")[0] + `@yopmail.com` : user.email,
        isActive: user.isActive || false,
        newUser: true,
        role: roleObj[user.role] || roleObj.user,
        assignedPolicies: [],
        restrictedPolicies: [],
        gender: user.gender,
        parent: null,
        createdAt: user.createdAt ? new Date(user.createdAt) : new Date()
      });

      // Check for parent-child relationships based on email or mobile
      // Check both email and mobile separately
      const identifiers = [];
      if (user.email) identifiers.push({ type: 'email', value: user.email });
      if (user.mobile) identifiers.push({ type: 'mobile', value: user.mobile });

      let foundParent = false;
      for (const { type, value } of identifiers) {
        const existingUser = emailMobileMap.get(value);
        if (existingUser) {
          // This is a child user - set parent reference
          userDoc.parent = existingUser.userDoc._id as Types.ObjectId;
          parentChildMap.set(value, existingUser.userDoc._id as Types.ObjectId);
          logger.info(`Row ${rowNumber}: Setting user ${user.firstName} ${user.lastName} as child of user from row ${existingUser.rowNumber} (same ${type}: ${value})`);
          foundParent = true;
          break; // Only need to find one parent relationship
        }
      }

      if (!foundParent) {
        // This is the first occurrence - potential parent
        for (const { value } of identifiers) {
          if (!emailMobileMap.has(value)) {
            emailMobileMap.set(value, { userDoc, rowNumber });
          }
        }
      }

      users.push(userDoc);

      const staffsRole = [
        global.roleMap.get(ENUM_ROLE_TYPE.WEB_MASTER).toString(),
        global.roleMap.get(ENUM_ROLE_TYPE.FRONT_DESK_ADMIN).toString(),
        global.roleMap.get(ENUM_ROLE_TYPE.TRAINER).toString(),
        global.roleMap.get(ENUM_ROLE_TYPE.FRONT_DESK_ADMIN).toString()
      ];

      const facility: IFacility = global.facilityMap.get(user.facilityId) ?? global.facilityMap.values().next()?.value ?? null;
      if (!facility) {
        errors.push(`Row ${rowNumber}: Facility not found for user ${user.firstName} ${user.lastName} or default facility not set`);
        continue;
      }

      if (staffsRole.includes(global.roleMap.get(user.role)?.toString())) {
        const staff = await getStaff({
          ...userDoc,
          photo: user.photo,
        }, user, global.config.organizationId, facility._id.toString());
        staffs.push(staff);
      } else if (user.role === ENUM_ROLE_TYPE.USER) {
        const client = await getClient(userDoc, user, global.config.organizationId, facility._id);
        clients.push(client);
      }
    }

    if (errors.length > 0) {
      logger.error('Validation errors found in user data:', errors);
      throw Error('Validation errors found in user data');
    }

    // Check for duplicate users only if we have usernames to check
    if (usernames.length > 0) {
      try {
        const existingUser = await User.find({
          organizationId: new Types.ObjectId(global.config.organizationId),
          $and: [
            {
              $or: [
                { email: { $in: usernames } },
                { mobile: { $in: usernames } }
              ]
            },
            {
              $or: [
                { parent: null },
                { parent: { $exists: false } }
              ]
            }
          ],
          parent: null
        }, { email: 1, mobile: 1 }).session(session).exec();

        if (existingUser.length) {
          logger.error(`Duplicate users found in database: ${usernames}`);
          const duplicateIdentifiers = existingUser.map((user: any) => `(${user.email || ''} ${user.mobile ? ', ' + user.mobile : ''})`);
          logger.error(`Duplicate users found in database: ${duplicateIdentifiers.join(', ')}`);
          await session.abortTransaction();
          return [];
        }
      } catch (err) {
        logger.error('Error checking for duplicate users:', err);
        await session.abortTransaction();
        throw err;
      }
    }

    const result = await User.insertMany(users, { session });
    logger.log(`Successfully migrated ${result.length} users to MongoDB`);

    await Staff.insertMany(staffs, { session });
    logger.log(`Successfully migrated ${staffs.length} staffs to MongoDB`);

    await Client.insertMany(clients, { session });
    logger.log(`Successfully migrated ${clients.length} clients to MongoDB`);

    // Commit the transaction
    await session.commitTransaction();
    logger.log('Transaction committed successfully');

    return users;
  } catch (error) {
    // Abort the transaction on error
    await session.abortTransaction();
    logger.error('Error in getValidateUsers, transaction aborted:', error);
    throw error;
  } finally {
    // End the session
    session.endSession();
  }
}

export async function getStaff(user: any, csvUser: ICsvUser, organization: string, facilityId: string): Promise<any> {
  const facilityIds = facilityId ? [new Types.ObjectId(facilityId)] : [];

  const address = await getStaffAddress(csvUser.city, csvUser.state, csvUser.address1, csvUser.postalCode, csvUser.country);

  const staff = new Staff({
    id: user.id,
    createdBy: new Types.ObjectId(organization),
    userId: user._id,
    organizationId: new Types.ObjectId(organization),
    facilityId: facilityIds,
    address,
    gender: csvUser.gender?.toLocaleLowerCase(),
    dateOfBirth: csvUser.dob ? new Date(csvUser.dob) : null,
    setUpDate: csvUser.createdAt ? new Date(csvUser.createdAt) : null,
    createdAt: csvUser.createdAt ? new Date(csvUser.createdAt) : new Date(),
  });
  const hash = crypto.createHash('sha256').update(uuid4() + (user.email || user.mobile) + staff._id.toString()).digest('hex');
  const randomId = hash.substring(0, 7);
  staff.staffId = `S-${randomId}`;
  return staff;
}

export async function getClient(user: any, csvUser: ICsvUser, organization: string, facilityId: Types.ObjectId): Promise<any> {
  const address = getClientAddress(csvUser.address1, csvUser.address2, csvUser.city, csvUser.state, csvUser.postalCode, csvUser.country);

  const client = new Client({
    id: user.id,
    createdBy: new Types.ObjectId(organization),
    userId: user._id,
    organizationId: new Types.ObjectId(organization),
    facilityId: new Types.ObjectId(facilityId),
    address,
    dob: csvUser.dob ? new Date(csvUser.dob) : null,
    gender: csvUser?.gender.toLowerCase(),
    relation: csvUser.relation ? csvUser.relation?.toLowerCase() : undefined,
    photo: csvUser.photo,
    emergencyContactPerson: csvUser.emergencyContactPerson,
    emergencyContactPhone: csvUser.emergencyContactPhone,
    createdAt: csvUser.createdAt ? new Date(csvUser.createdAt) : new Date(),
  });

  client.clientId = `C-${generateUniqueClientId(user.email || user.mobile.toString())}`;
  duplicateClientsIds.add(client.clientId);
  return client;
}

function generateUniqueClientId(email) {
  const base62chars = 'OPQRSTUVWX0123456789ABCDEFGHIJKLMNYZabcdefghijklmnopqrstuvwxyz';

  function toBase62(num) {
    let str = '';
    while (num > 0) {
      str = base62chars[num % 62] + str;
      num = Math.floor(num / 62);
    }
    return str.padStart(7, '0').slice(0, 7);
  }

  function simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = (hash * 31 + str.charCodeAt(i)) >>> 0;
    }
    return hash;
  }

  function generateCode(input) {
    const hash = simpleHash(input);
    const base62 = toBase62(hash);
    return `${base62}`;
  }

  let attempts = 0;
  const baseInput = String(email || '');
  let clientId = '';

  if (!baseInput) {
    throw new Error("User must have at least an email or mobile.");
  }

  while (!clientId) {
    const salt = attempts === 0 ? '' : `-${uuid4()}`;
    const input = `${baseInput}${salt}`;
    const code = generateCode(input);

    if (!duplicateClientsIds.has(code)) {
      clientId = code;
      duplicateClientsIds.add(code);
      break;
    }

    attempts++;

    if (attempts > 100) {
      throw new Error("Failed to generate unique client ID after 100 attempts.");
    }
  }

  return clientId;
}

const getStaffAddress = async (city: string, state: string, street: string, postalCode: string, country: string) => {
  // Make sure the maps are initialized
  const stateId = global.stateMap.get(state?.toLowerCase());
  const cityId = global.cityMap.get(city?.toLowerCase());

  const address = {
    stateId,
    cityId,
    street,
    postalCode,
    country,
  };

  if (!address.stateId) {
    logger.warn(`State not found for "${state}". Using default state.`);
    // Instead of throwing an error, we could use a default state ID or null
    address.stateId = null;
  }

  if (!address.cityId) {
    logger.warn(`City not found for "${city}". Using default city.`);
    // Instead of throwing an error, we could use a default city ID or null
    address.cityId = null;
  }

  return address;
}

const getClientAddress = (addressLine1: string, addressLine2: string, city: string, state: string, postalCode: string, country: string) => {

  if (!state) {
    throw new Error(`State is required for client address`);
  }

  const address = {
    addressLine1: addressLine1,
    addressLine2: addressLine2,
    city: city ? global.cityMap.get(city?.toLowerCase()) : null,
    state: state ? global.stateMap.get(state?.toLowerCase()) : null,
    postalCode: postalCode && !isNaN(Number(postalCode)) ? Number(postalCode) : undefined,
    country: country ? country : "",
    isDefault: false
  };

  return address;
}